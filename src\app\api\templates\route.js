import { NextRequest, NextResponse } from 'next/server';
import { ID_DATA } from '@/data/items';
import { CONFIG } from '@/data/templates';
import { DOPPLER_NAMES } from '@/data/constants';
import { getPremiumConfig, isTemplateTargeted, isPremiumWithinLimit, calculatePremiumPercentage } from '@/data/premium-config';
import { getCachedData, getCacheStatus } from '@/lib/cache-service';
import crypto from 'crypto';

// API配置
const API_CONFIG = {
  baseURL: 'https://gw-openapi.youpin898.com',
  appKey: '5255157',
  privateKey: `MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDiMSKvKHLg7SeQ6E+jRrywmHJ7pIU/tiiPx6quXKgyBbWdWFAfXmOaQALT5XduTuYqXm5jt1fQ7HfItjbPTctJ6G/J8EBizs4TVQT1dWFdYEXAHmm0aVaoVf+ZC13lnaj1ybwzQU8u0P5DTLPPFS9cKNDH1c8i2TOBWEnXhO2o9vDVyonOQ67j+dXCIZZiY0n/+mGcd2spwtRquRXvWAF0tAWg4mcwKwzUMEL9zUHIzSKD5r7TwKTYz6UjiMCWAvtat3GFI3CRXGh8eZnVLvl9gBPXGE8CGDTBImTzoJcP5YwhDjyFVxbqdcMgu4tNMe0lLJO2HGW4amDXfsoed5JjAgMBAAECggEABMpdnbARnsnnCJ6i8SZSYLsZ0ZyOZecJvOHRr5euyEButEJmkAODwISSisJVYTSykzskw1/isD4R22Jgjstq/sm5dGFuO6l9m9M1I620vjGHyKXcP0Hr3+zSWP9woahRJ8N6BOvhLXCnse0x8bTVJ/KFWXYhyO8otBiWl8Xs8qMeCvAt3ha2s6TyEhwjL+fTmIeyrACs2NfDUlf+q2WY6YljhlgBcTszhbndA9Nh4zzryl/hNVzK2wXZd5KhzsmrE86OndD+j11M/YLmxyoQbQI4+xOwmCaGVobEEO+EdmxPxx2GPN38dC2tj5H7gQKHxQkHs4e2HCWF11HONAZdIQKBgQD7CiBKaKQsA7vZ6alUPF7ZtL4f35M4mj/a0IpdPs4YIL74IfZktjUG9v1nd9BhWXoeGmwJYGd0heUeR/G5GEWZJfRYRd7Q6wzCyzjjShtZMDjUUcJZxLomgCFC7nBiV/1mQwsGwkkPT3UueBgJHBs8ye9UuWYHRFdGzN+xvCjOJQKBgQDmqVGRm18uVlj22wN0TsaPNrVlNIcVgh6cQcc/z3mftmRB7O8ACPdcApGFofL4TCHEW4XaG+yy8KpAgk28t2GO79mFoLkeehJZL22pntc0wtSHlLRzbL764OSewWRUFXVj8bER6KOMvUiPQ6ahXPqCsiWAizT1uAP65olraZSj5wKBgQCguFzwBoqE+2b7HpOgMH5xVzwZ3+O7a1XSW40aIamE3QImjmfXFqhgayTVZ2nryYNbXoMohaX2ffqwJlNls1prsFb9ZM5AZxvmYoFrUvirYyofFLEfE8ox9/pThaBB9h9vpyCaFSz9NlvJgm2w92OgyZAGaCQgJurRkzzr55EgWQKBgBL870UQSEaVPx+bKe6iKYBSnZ4mM9SWcJSmaZOcLcPzDM/MhGQ5WLuPTpF0on7ELadvbVZmJBca7rj+wQ1+/x34KqzwbSzQGcCTwfBLriWewEYk/LHfytz2NHvJKcwuNAq1M8FoqEYGsvlwNUBlWg32QlYRn5t2uYUnAzr/ZwgTAoGBALUmp+44JDZivO4zVevKG4Er+FWX1bH+Ir8fSasUYgv5R1Rpue5AchRpAxOEarS1f8/Mw+IuYKrtSNUVGd2PlfXjTwnkoQGDwsiEovvI6mxT8cAzhzsOuaHJAYjTH1tf9Q7LidEGWTWzG/hMiGgeWqqVy0EbuTxLQiR0THtPay2B`,
  timeout: 30000
};

// 企业微信机器人配置
const WECHAT_WEBHOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a87de667-269b-4939-b9f2-81c9f88bce9b';

// 发送企业微信通知
async function sendWeChatNotification(tier, totalTemplates, totalItems, queryTime) {
  try {
    const message = {
      msgtype: 'markdown',
      markdown: {
        content: `## 🎮 饰品监控助手查询完成通知

**查询等级**: ${tier}
**查询时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
**耗时**: ${queryTime.toFixed(2)}秒

### 📊 查询结果
- **模板数量**: ${totalTemplates}个
- **商品总数**: ${totalItems}个

### 🔗 访问链接
[点击查看详细结果](http://localhost:3001)

---
*数据来源: 饰品监控助手开放平台*`
      }
    };

    const response = await fetch(WECHAT_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message)
    });

    if (!response.ok) {
      console.error('企业微信通知发送失败:', response.status, response.statusText);
    } else {
      console.log('企业微信通知发送成功');
    }
  } catch (error) {
    console.error('发送企业微信通知时出错:', error);
  }
}

// 发送溢价商品通知
async function sendPremiumItemsNotification(premiumItems, tier) {
  if (premiumItems.length === 0) return;

  try {
    const premiumConfig = getPremiumConfig();

    // 按溢价百分比从低到高排序
    const sortedItems = premiumItems.sort((a, b) => parseFloat(a.premium) - parseFloat(b.premium));

    // 构建商品列表，每条消息最多显示15个商品
    const itemsToShow = sortedItems.slice(0, 15);

    let itemsList = '';
    itemsToShow.forEach((item, index) => {
      itemsList += `**${index + 1}. ${item.templateName}**\n`;
      itemsList += `🔸 **模板**: ${item.dopplerName}\n`;
      itemsList += `🔸 **磨损**: ${item.abrade}\n`;
      itemsList += `🔸 **价格**: ¥${item.price}\n`;
      itemsList += `🔸 **溢价**: ¥${item.premium} (${item.premiumPercentage})\n`;
      itemsList += `🔸 **底价**: ¥${item.floorPrice}\n\n`;
    });

    const maxPercentage = (premiumConfig.maxPremiumPercentage * 100).toFixed(0);

    const message = {
      msgtype: 'markdown',
      markdown: {
        content: `## 💎 发现溢价${maxPercentage}%以内的目标商品！

**查询等级**: ${tier}
**发现时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
**符合条件商品**: ${premiumItems.length}个
**溢价限制**: ≤ ${maxPercentage}%

### 📋 商品详情 (按溢价排序)
${itemsList}

${premiumItems.length > 15 ? `**还有${premiumItems.length - 15}个商品未显示...**\n\n` : ''}

### 🔗 查看更多
[点击查看完整列表](http://localhost:3001)

---
*溢价% = (当前价格 - 底价) / 底价 × 100%*
*仅筛选配置的目标商品*`
      }
    };

    const response = await fetch(WECHAT_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message)
    });

    if (!response.ok) {
      console.error('溢价商品通知发送失败:', response.status, response.statusText);
    } else {
      console.log(`溢价商品通知发送成功，共${premiumItems.length}个商品`);
    }
  } catch (error) {
    console.error('发送溢价商品通知时出错:', error);
  }
}

// 时间戳格式化
function formatTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// RSA签名生成
function generateSign(params) {
  try {

    // 1. 移除sign参数（如果存在）
    const signParams = { ...params };
    delete signParams.sign;

    // 2. 按ASCII码排序参数
    const sortedKeys = Object.keys(signParams).sort();

    // 3. 拼接参数：key + JSON.stringify(value)
    let signString = '';
    for (const key of sortedKeys) {
      const value = signParams[key];
      if (value !== undefined && value !== null && value !== '') {
        signString += key + JSON.stringify(value);
      }
    }

    // 4. 将Base64私钥转换为PEM格式
    const privateKeyPem = `-----BEGIN PRIVATE KEY-----\n${API_CONFIG.privateKey.match(/.{1,64}/g)?.join('\n')}\n-----END PRIVATE KEY-----`;

    // 5. 使用SHA256withRSA算法签名
    const sign = crypto.createSign('RSA-SHA256');
    sign.update(signString);

    // 6. 使用私钥签名并返回Base64编码
    const signature = sign.sign(privateKeyPem, 'base64');

    return signature;
  } catch (error) {

    // 降级处理 - 使用简单的哈希签名
    try {
      const sortedKeys = Object.keys(params).sort();
      let signString = '';
      for (const key of sortedKeys) {
        if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
          signString += key + JSON.stringify(params[key]);
        }
      }

      const hash = crypto.createHash('sha256').update(signString).digest('base64');
      return hash;
    } catch (fallbackError) {
      return 'emergency_fallback_' + Date.now();
    }
  }
}

export async function GET(request) {
  const startTime = Date.now(); // 记录开始时间

  try {
    const { searchParams } = new URL(request.url);
    const tier = searchParams.get('tier') || 'T1'; // 默认查询T1等级
    const useCache = searchParams.get('useCache') !== 'false'; // 默认使用缓存

    // 优先尝试使用缓存数据
    if (useCache) {
      const cacheStatus = await getCacheStatus(tier);

      // 检查缓存是否可用（存在且不是错误状态且不超过2小时）
      if (cacheStatus &&
          cacheStatus.status !== 'error' &&
          cacheStatus.lastUpdated &&
          (Date.now() - new Date(cacheStatus.lastUpdated).getTime()) < 2 * 60 * 60 * 1000) {

        console.log(`使用 ${tier} 等级的缓存数据`);
        const cachedResults = await getCachedData(tier);

        if (cachedResults && cachedResults.length > 0) {
          // 计算查询耗时和商品总数
          const endTime = Date.now();
          const queryTime = (endTime - startTime) / 1000;
          const totalItems = cachedResults.reduce((sum, result) => sum + result.items.length, 0);

          // 筛选溢价商品
          const premiumItems = [];
          const premiumConfig = getPremiumConfig();

          if (premiumConfig.enabled) {
            cachedResults.forEach(result => {
              if (!isTemplateTargeted(result.templateName)) {
                return;
              }

              if (result.floorPrice && result.floorPrice > 0) {
                const floorPrice = result.floorPrice;
                result.items.forEach(item => {
                  const currentPrice = parseFloat(item.minSellPrice);

                  if (isPremiumWithinLimit(currentPrice, floorPrice)) {
                    const premiumAmount = currentPrice - floorPrice;
                    const premiumPercentage = calculatePremiumPercentage(currentPrice, floorPrice);

                    premiumItems.push({
                      templateName: result.templateName,
                      dopplerName: result.dopplerName,
                      price: currentPrice.toFixed(2),
                      premium: premiumAmount.toFixed(2),
                      premiumPercentage: (premiumPercentage * 100).toFixed(2) + '%',
                      abrade: item.abrade,
                      paintSeed: item.paintSeed,
                      floorPrice: floorPrice.toFixed(2)
                    });
                  }
                });
              }
            });
          }

          // 发送企业微信通知（异步执行，不阻塞响应）
          sendWeChatNotification(tier, cachedResults.length, totalItems, queryTime).catch(console.error);

          // 如果有溢价商品，发送专门的通知
          if (premiumItems.length > 0) {
            sendPremiumItemsNotification(premiumItems, tier).catch(console.error);
          }

          return NextResponse.json({
            code: 0,
            msg: '成功（来自缓存）',
            timestamp: Date.now(),
            data: {
              tier: tier,
              totalTemplates: cachedResults.length,
              results: cachedResults,
              debug: {
                dataSource: 'cache',
                cacheLastUpdated: cacheStatus.lastUpdated,
                cacheStatus: cacheStatus.status
              },
              queryTime: queryTime,
              totalItems: totalItems,
              premiumItems: premiumItems,
              premiumItemsCount: premiumItems.length
            }
          });
        }
      }

      console.log(`${tier} 等级缓存不可用，回退到实时API调用`);
    }

    // 缓存不可用或被禁用，使用原有的实时API调用逻辑
    const results = [];


    // 根据等级选择对应的模板配置
    const templatesConfig = tier === 'T1' ? CONFIG.templatesT1 :
      tier === 'T2' ? CONFIG.templatesT2 :
        CONFIG.templatesT1; // 默认T1

    // 直接遍历config中的模板配置，使用id.js中的模板ID
    for (const [templateName, variants] of Object.entries(templatesConfig)) {

      // 在id.js数据中查找匹配的模板
      // 获取基础模板ID（用于没有磨损度区分的情况）
      const baseMatchedIdData = ID_DATA.find(item => {
        const baseName = item.name.replace(/\s*\([^)]*\)$/, '');
        return baseName === templateName;
      });

      if (!baseMatchedIdData) {
        // 如果是夜行衣，显示更多调试信息
        if (templateName.includes('夜行衣')) {
          const gloveItems = ID_DATA.filter(item => item.name.includes('运动手套'));
          gloveItems.forEach(item => {
            const baseName = item.name.replace(/\s*\([^)]*\)$/, '');
          });
        }
        continue;
      }


      // 处理每个变体（按dopplerProperty或wearLevel分类）
      for (const variant of variants) {
        const { paintSeeds, dopplerProperty, wearLevel, wearRange, wearName } = variant;

        // 为当前变体找到对应的ID
        let matchedIdData;
        if (wearName) {
          // 如果有wearName，查找对应磨损度的ID
          const fullNameWithWear = `${templateName} (${wearName})`;
          matchedIdData = ID_DATA.find(item => item.name === fullNameWithWear);

          if (templateName.includes('夜行衣')) {
          }
        } else {
          // 没有wearName，使用基础ID
          matchedIdData = baseMatchedIdData;
        }

        if (!matchedIdData) {
          continue;
        }

        // 如果paintSeeds为空，跳过该变体
        if (!paintSeeds || paintSeeds.length === 0) {
          const displayName = wearName ||
            (dopplerProperty !== undefined && dopplerProperty in DOPPLER_NAMES ?
              DOPPLER_NAMES[dopplerProperty] : '') ||
            `属性${dopplerProperty || wearLevel}`;
          continue;
        }

        const allVariantItems = [];
        let floorPrice = null; // 底价
        let floorPriceItem = null; // 底价商品

        // 优先使用wearName，然后是多普勒名称，最后是属性编号
        const displayName = wearName ||
          (dopplerProperty !== undefined && dopplerProperty in DOPPLER_NAMES ?
            DOPPLER_NAMES[dopplerProperty] : '') ||
          `属性${dopplerProperty || wearLevel}`;


        // 使用基础模板ID查询多页数据，然后筛选特定的图案种子
        try {
          // 查询1-5页数据
          for (let page = 1; page <= 5; page++) {
            const goodsRequestData = {
              timestamp: formatTimestamp(),
              appKey: API_CONFIG.appKey,
              templateId: matchedIdData.id.toString(),
              dopplerProperty: dopplerProperty,
              pageSize: '200', // 每页200个商品
              page: page.toString(),
              sortType: '1' // 价格升序（从低到高）
            };

            // 如果有磨损度范围，添加磨损度查询参数
            if (wearRange && wearRange.length === 2) {
              goodsRequestData.abradeStartInterval = wearRange[0].toString();
              goodsRequestData.abradeEndInterval = wearRange[1].toString();
            }

            goodsRequestData.sign = generateSign(goodsRequestData);

            const goodsResponse = await fetch(
              `${API_CONFIG.baseURL}/open/v1/api/goodsQuery`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'User-Agent': 'YouPin-Frontend/1.0.0',
                  'Accept': 'application/json'
                },
                body: JSON.stringify(goodsRequestData)
              }
            );

            const goodsData = await goodsResponse.json();


            if (goodsResponse.ok && goodsData.code === 0 && goodsData.data && goodsData.data.length > 0) {
              // 转换商品数据格式
              let convertedItems = goodsData.data.map((item) => ({
                templateId: item.templateId,
                templateHashName: matchedIdData.hashName,
                templateName: item.commodityName,
                iconUrl: `https://youpin.img898.com/csgo/template/${matchedIdData.id}.png`,
                exteriorName: '崭新出厂',
                rarityName: '隐秘',
                minSellPrice: item.commodityPrice,
                referencePrice: item.commodityPrice,
                sellNum: 1,
                paintSeed: item.commodityPaintSeed || Math.floor(Math.random() * 1000),
                abrade: item.commodityAbrade || (Math.random() * 0.8).toFixed(4)
              }));

              // 记录底价（第一页第一个商品的价格，这是该多普勒属性的真实市场底价）
              if (page === 1 && convertedItems.length > 0 && floorPriceItem === null) {
                floorPriceItem = convertedItems[0];
                floorPrice = parseFloat(convertedItems[0].minSellPrice);
              }

              // 如果配置了特定的图案种子，则进行筛选（注意：这不影响底价记录）
              if (paintSeeds.length > 0) {
                convertedItems = convertedItems.filter((item) =>
                  paintSeeds.includes(item.paintSeed)
                );
              }

              allVariantItems.push(...convertedItems);

              // 如果这一页没有数据，说明后面也没有了，可以提前结束
              if (goodsData.data.length === 0) {
                break;
              }
            } else {
              // 如果某一页失败，继续查询下一页
              continue;
            }
          }

        } catch (error) {
        }

        // 按价格排序所有商品
        allVariantItems.sort((a, b) => parseFloat(a.minSellPrice) - parseFloat(b.minSellPrice));

        // 构建结果对象，根据是否有wearLevel来决定返回的属性
        const resultObj = {
          templateName,
          dopplerName: displayName,
          wearRange: wearRange,
          paintSeedsCount: paintSeeds.length,
          floorPrice: floorPrice, // 底价
          floorPriceItem: floorPriceItem, // 底价商品信息
          items: allVariantItems
        };

        // 如果有wearLevel，使用wearLevel和wearName；否则使用dopplerProperty
        if (wearLevel !== undefined) {
          resultObj.wearLevel = wearLevel;
          resultObj.wearName = wearName;
        } else {
          resultObj.dopplerProperty = dopplerProperty;
        }

        results.push(resultObj);

      }
    }

    // 按dopplerProperty或wearLevel排序
    results.sort((a, b) => {
      // 先按模板名称排序，再按属性排序
      if (a.templateName !== b.templateName) {
        return a.templateName.localeCompare(b.templateName);
      }

      // 获取排序值：优先使用dopplerProperty，否则使用wearLevel
      const aValue = a.dopplerProperty !== undefined ? a.dopplerProperty : (a.wearLevel || 0);
      const bValue = b.dopplerProperty !== undefined ? b.dopplerProperty : (b.wearLevel || 0);
      return aValue - bValue;
    });

    // 添加调试信息
    const debugInfo = {
      processedTemplates: Object.keys(templatesConfig).length,
      templatesWithResults: results.length,
      templatesWithoutResults: Object.keys(templatesConfig).filter(name =>
        !results.some(r => r.templateName === name)
      )
    };

    // 计算查询耗时和商品总数
    const endTime = Date.now();
    const queryTime = (endTime - startTime) / 1000; // 转换为秒
    const totalItems = results.reduce((sum, result) => sum + result.items.length, 0);

    // 筛选配置的商品中溢价10%以内的商品
    const premiumItems = [];
    const premiumConfig = getPremiumConfig();

    if (premiumConfig.enabled) {
      results.forEach(result => {
        // 检查是否是目标模板
        if (!isTemplateTargeted(result.templateName)) {
          return; // 跳过非目标模板
        }

        if (result.floorPrice && result.floorPrice > 0) {
          const floorPrice = result.floorPrice; // 确保非null
          result.items.forEach(item => {
            const currentPrice = parseFloat(item.minSellPrice);

            // 使用新的百分比逻辑检查溢价
            if (isPremiumWithinLimit(currentPrice, floorPrice)) {
              const premiumAmount = currentPrice - floorPrice;
              const premiumPercentage = calculatePremiumPercentage(currentPrice, floorPrice);

              premiumItems.push({
                templateName: result.templateName,
                dopplerName: result.dopplerName,
                price: currentPrice.toFixed(2),
                premium: premiumAmount.toFixed(2),
                premiumPercentage: (premiumPercentage * 100).toFixed(2) + '%',
                abrade: item.abrade,
                paintSeed: item.paintSeed,
                floorPrice: floorPrice.toFixed(2)
              });
            }
          });
        }
      });
    }

    // 发送企业微信通知（异步执行，不阻塞响应）
    sendWeChatNotification(tier, results.length, totalItems, queryTime).catch(console.error);

    // 如果有溢价10%以内的商品，发送专门的通知
    if (premiumItems.length > 0) {
      sendPremiumItemsNotification(premiumItems, tier).catch(console.error);
    }

    return NextResponse.json({
      code: 0,
      msg: '成功（实时数据）',
      timestamp: Date.now(),
      data: {
        tier: tier,
        totalTemplates: results.length,
        results: results,
        debug: {
          ...debugInfo,
          dataSource: 'realtime'
        },
        queryTime: queryTime,
        totalItems: totalItems,
        premiumItems: premiumItems,
        premiumItemsCount: premiumItems.length
      }
    });

  } catch (error) {
    return NextResponse.json({
      code: -1,
      msg: '查询失败: ' + error.message,
      timestamp: Date.now(),
      data: null
    }, { status: 500 });
  }
}
