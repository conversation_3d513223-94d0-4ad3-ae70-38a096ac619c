-- CreateTable
CREATE TABLE "cached_templates" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "template_name" TEXT NOT NULL,
    "doppler_name" TEXT NOT NULL,
    "doppler_property" INTEGER,
    "wear_level" INTEGER,
    "wear_name" TEXT,
    "wear_range" TEXT,
    "paint_seeds_count" INTEGER NOT NULL,
    "floor_price" REAL,
    "floor_price_item" TEXT,
    "tier" TEXT NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "cached_items" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "template_id" TEXT NOT NULL,
    "template_hash_name" TEXT NOT NULL,
    "template_name" TEXT NOT NULL,
    "icon_url" TEXT NOT NULL,
    "exterior_name" TEXT NOT NULL,
    "rarity_name" TEXT NOT NULL,
    "min_sell_price" REAL NOT NULL,
    "reference_price" REAL NOT NULL,
    "sell_num" INTEGER NOT NULL,
    "paint_seed" INTEGER NOT NULL,
    "abrade" TEXT NOT NULL,
    "cached_template_id" INTEGER NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "cached_items_cached_template_id_fkey" FOREIGN KEY ("cached_template_id") REFERENCES "cached_templates" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "cache_status" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "tier" TEXT NOT NULL,
    "last_updated" DATETIME NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'idle',
    "error_msg" TEXT,
    "total_items" INTEGER NOT NULL DEFAULT 0,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_users" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "email" TEXT NOT NULL,
    "password_hash" TEXT NOT NULL,
    "username" TEXT,
    "role" TEXT NOT NULL DEFAULT 'user',
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "email_verified" BOOLEAN NOT NULL DEFAULT false,
    "verification_token" TEXT
);
INSERT INTO "new_users" ("created_at", "email", "email_verified", "id", "is_active", "password_hash", "updated_at", "username", "verification_token") SELECT "created_at", "email", "email_verified", "id", "is_active", "password_hash", "updated_at", "username", "verification_token" FROM "users";
DROP TABLE "users";
ALTER TABLE "new_users" RENAME TO "users";
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE INDEX "cached_templates_tier_idx" ON "cached_templates"("tier");

-- CreateIndex
CREATE INDEX "cached_templates_template_name_idx" ON "cached_templates"("template_name");

-- CreateIndex
CREATE INDEX "cached_items_cached_template_id_idx" ON "cached_items"("cached_template_id");

-- CreateIndex
CREATE INDEX "cached_items_paint_seed_idx" ON "cached_items"("paint_seed");

-- CreateIndex
CREATE INDEX "cached_items_min_sell_price_idx" ON "cached_items"("min_sell_price");

-- CreateIndex
CREATE UNIQUE INDEX "cache_status_tier_key" ON "cache_status"("tier");
