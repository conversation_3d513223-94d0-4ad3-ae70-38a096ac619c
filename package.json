{"name": "youpin-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "start:cache": "node scripts/start-with-cache.js", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.12.0", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "next": "15.4.1", "node-cron": "^4.2.1", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4"}}