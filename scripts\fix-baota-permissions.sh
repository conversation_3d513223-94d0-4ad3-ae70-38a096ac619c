#!/bin/bash

# 宝塔面板权限修复脚本
# 专门解决在宝塔面板上部署 Node.js 应用时的权限问题
# 使用方法: chmod +x scripts/fix-baota-permissions.sh && ./scripts/fix-baota-permissions.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}🔧 宝塔面板权限修复工具${NC}"
echo "=================================================="

# 检查是否为 Linux 环境
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo -e "${RED}❌ 此脚本仅适用于 Linux 环境 (宝塔面板)${NC}"
    exit 1
fi

# 检查是否有 sudo 权限
if [ "$EUID" -ne 0 ]; then
    echo -e "${YELLOW}⚠️ 建议使用 root 权限运行此脚本${NC}"
    echo -e "${BLUE}如果遇到权限问题，请使用: sudo ./scripts/fix-baota-permissions.sh${NC}"
fi

echo -e "${BLUE}🔍 检查当前目录权限...${NC}"
pwd
ls -la

echo -e "${BLUE}🔧 开始修复权限问题...${NC}"

# 1. 修复项目根目录权限
echo -e "${BLUE}1. 修复项目根目录权限...${NC}"
chmod 755 .
chown -R www:www . 2>/dev/null || chown -R nginx:nginx . 2>/dev/null || echo "无法更改所有者，继续..."

# 2. 修复 node_modules 权限
if [ -d "node_modules" ]; then
    echo -e "${BLUE}2. 修复 node_modules 权限...${NC}"
    chmod -R 755 node_modules/
    
    # 特别修复 .bin 目录
    if [ -d "node_modules/.bin" ]; then
        echo -e "${BLUE}   修复 node_modules/.bin 目录...${NC}"
        chmod -R 755 node_modules/.bin/
        
        # 修复所有可执行文件
        find node_modules/.bin/ -type f -exec chmod +x {} \;
        
        # 特别检查 next 命令
        if [ -f "node_modules/.bin/next" ]; then
            chmod +x node_modules/.bin/next
            echo -e "${GREEN}   ✅ next 命令权限已修复${NC}"
            ls -la node_modules/.bin/next
        else
            echo -e "${RED}   ❌ 未找到 next 命令文件${NC}"
        fi
    fi
else
    echo -e "${RED}❌ node_modules 目录不存在，请先运行 npm install${NC}"
fi

# 3. 修复脚本文件权限
echo -e "${BLUE}3. 修复脚本文件权限...${NC}"
if [ -d "scripts" ]; then
    find scripts/ -name "*.sh" -exec chmod +x {} \;
    chmod -R 755 scripts/
fi

# 4. 修复数据目录权限
echo -e "${BLUE}4. 修复数据目录权限...${NC}"
if [ -d "data" ]; then
    chmod -R 755 data/
    chown -R www:www data/ 2>/dev/null || chown -R nginx:nginx data/ 2>/dev/null || echo "无法更改数据目录所有者"
fi

# 5. 修复日志目录权限
echo -e "${BLUE}5. 修复日志目录权限...${NC}"
if [ -d "logs" ]; then
    chmod -R 755 logs/
    chown -R www:www logs/ 2>/dev/null || chown -R nginx:nginx logs/ 2>/dev/null || echo "无法更改日志目录所有者"
fi

# 6. 修复 .next 构建目录权限
echo -e "${BLUE}6. 修复 .next 构建目录权限...${NC}"
if [ -d ".next" ]; then
    chmod -R 755 .next/
fi

# 7. 修复 prisma 目录权限
echo -e "${BLUE}7. 修复 prisma 目录权限...${NC}"
if [ -d "prisma" ]; then
    chmod -R 755 prisma/
fi

# 8. 检查并修复 PM2 相关权限
echo -e "${BLUE}8. 检查 PM2 配置...${NC}"
if [ -f "ecosystem.config.js" ]; then
    chmod 644 ecosystem.config.js
    echo -e "${GREEN}   ✅ PM2 配置文件权限已修复${NC}"
fi

# 9. 验证修复结果
echo -e "${BLUE}9. 验证修复结果...${NC}"
echo ""
echo -e "${BLUE}项目根目录权限:${NC}"
ls -ld .

if [ -f "node_modules/.bin/next" ]; then
    echo -e "${BLUE}next 命令权限:${NC}"
    ls -la node_modules/.bin/next
    
    echo -e "${BLUE}测试 next 命令:${NC}"
    if node_modules/.bin/next --version 2>/dev/null; then
        echo -e "${GREEN}✅ next 命令可以正常执行${NC}"
    else
        echo -e "${RED}❌ next 命令仍然无法执行${NC}"
    fi
fi

echo ""
echo -e "${GREEN}🎉 权限修复完成！${NC}"
echo ""
echo -e "${BLUE}接下来的步骤:${NC}"
echo "1. 在宝塔面板中重新启动应用"
echo "2. 或者使用命令: pm2 restart youpin-sentinel"
echo "3. 如果仍有问题，请检查宝塔面板的 Node.js 版本设置"
echo ""
echo -e "${YELLOW}常见宝塔问题解决方案:${NC}"
echo "• 确保宝塔面板中 Node.js 版本 >= 18"
echo "• 确保项目路径设置正确"
echo "• 确保启动文件设置为: node_modules/.bin/next"
echo "• 确保启动参数设置为: start"
echo "• 如果使用 PM2，确保 PM2 配置正确"
