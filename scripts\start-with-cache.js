#!/usr/bin/env node

/**
 * 启动脚本 - 确保定时任务正常运行
 * 使用方法: node scripts/start-with-cache.js
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动饰品监控助手...');
console.log('📅 定时任务将在每小时的0分和30分执行缓存更新');

// 启动Next.js应用
const nextProcess = spawn('npm', ['start'], {
  cwd: path.resolve(__dirname, '..'),
  stdio: 'inherit',
  shell: true
});

nextProcess.on('error', (error) => {
  console.error('❌ 启动失败:', error);
  process.exit(1);
});

nextProcess.on('close', (code) => {
  console.log(`🔚 应用已退出，退出码: ${code}`);
  process.exit(code);
});

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭应用...');
  nextProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭应用...');
  nextProcess.kill('SIGTERM');
});
