#!/bin/bash

# 宝塔面板快速权限修复脚本
# 一键解决 "Permission denied" 问题

echo "🔧 宝塔面板快速权限修复..."

# 修复 node_modules/.bin 权限
if [ -d "node_modules/.bin" ]; then
    echo "修复 node_modules/.bin 权限..."
    chmod -R 755 node_modules/.bin/
    chmod +x node_modules/.bin/*
    echo "✅ node_modules/.bin 权限已修复"
else
    echo "❌ node_modules 不存在，请先运行: npm install"
    exit 1
fi

# 特别修复 next 命令
if [ -f "node_modules/.bin/next" ]; then
    chmod +x node_modules/.bin/next
    echo "✅ next 命令权限已修复"
    echo "next 命令信息:"
    ls -la node_modules/.bin/next
else
    echo "❌ next 命令不存在"
fi

# 修复项目权限
chmod 755 .
chmod -R 755 scripts/ 2>/dev/null || true
chmod -R 755 data/ 2>/dev/null || true
chmod -R 755 logs/ 2>/dev/null || true

echo "🎉 快速修复完成！"
echo ""
echo "现在可以尝试重新启动应用:"
echo "pm2 restart youpin-sentinel"
echo "或者在宝塔面板中点击重启"
