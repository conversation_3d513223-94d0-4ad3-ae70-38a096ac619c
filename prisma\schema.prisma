// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id                Int      @id @default(autoincrement())
  email             String   @unique
  passwordHash      String   @map("password_hash")
  username          String?
  role              String   @default("user") // 'user' 或 'super_admin'
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  isActive          Boolean  @default(true) @map("is_active")
  emailVerified     Boolean  @default(false) @map("email_verified")
  verificationToken String?  @map("verification_token")

  // 关联关系
  subscriptions Subscription[]
  payments      Payment[]

  @@map("users")
}

// 订阅表
model Subscription {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  planType  String   @map("plan_type") // 'monthly' 或 'yearly'
  status    String   @default("active") // 'active', 'expired', 'cancelled'
  startDate DateTime @map("start_date")
  endDate   DateTime @map("end_date")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  user     User      @relation(fields: [userId], references: [id])
  payments Payment[]

  @@map("subscriptions")
}

// 支付记录表
model Payment {
  id             Int       @id @default(autoincrement())
  userId         Int       @map("user_id")
  subscriptionId Int?      @map("subscription_id")
  amount         Float
  currency       String    @default("CNY")
  paymentMethod  String?   @map("payment_method") // 'alipay', 'wechat'
  paymentStatus  String    @default("pending") @map("payment_status") // 'pending', 'completed', 'failed', 'refunded'
  transactionId  String?   @map("transaction_id") // 第三方支付平台的交易ID
  createdAt      DateTime  @default(now()) @map("created_at")
  completedAt    DateTime? @map("completed_at")

  // 关联关系
  user         User          @relation(fields: [userId], references: [id])
  subscription Subscription? @relation(fields: [subscriptionId], references: [id])

  @@map("payments")
}

// 缓存模板表
model CachedTemplate {
  id               Int      @id @default(autoincrement())
  templateName     String   @map("template_name")
  dopplerName      String   @map("doppler_name")
  dopplerProperty  Int?     @map("doppler_property")
  wearLevel        Int?     @map("wear_level")
  wearName         String?  @map("wear_name")
  wearRange        String?  @map("wear_range") // JSON string for wear range array
  paintSeedsCount  Int      @map("paint_seeds_count")
  floorPrice       Float?   @map("floor_price")
  floorPriceItem   String?  @map("floor_price_item") // JSON string for floor price item
  tier             String   // T1, T2, etc.
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // 关联关系
  items CachedItem[]

  @@map("cached_templates")
  @@index([tier])
  @@index([templateName])
}

// 缓存商品表
model CachedItem {
  id                 Int      @id @default(autoincrement())
  templateId         String   @map("template_id")
  templateHashName   String   @map("template_hash_name")
  templateName       String   @map("template_name")
  iconUrl            String   @map("icon_url")
  exteriorName       String   @map("exterior_name")
  rarityName         String   @map("rarity_name")
  minSellPrice       Float    @map("min_sell_price")
  referencePrice     Float    @map("reference_price")
  sellNum            Int      @map("sell_num")
  paintSeed          Int      @map("paint_seed")
  abrade             String   // Float as string
  cachedTemplateId   Int      @map("cached_template_id")
  createdAt          DateTime @default(now()) @map("created_at")

  // 关联关系
  cachedTemplate CachedTemplate @relation(fields: [cachedTemplateId], references: [id], onDelete: Cascade)

  @@map("cached_items")
  @@index([cachedTemplateId])
  @@index([paintSeed])
  @@index([minSellPrice])
}

// 缓存状态表
model CacheStatus {
  id          Int      @id @default(autoincrement())
  tier        String   @unique // T1, T2, etc.
  lastUpdated DateTime @map("last_updated")
  status      String   @default("idle") // idle, updating, error
  errorMsg    String?  @map("error_msg")
  totalItems  Int      @default(0) @map("total_items")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("cache_status")
}
