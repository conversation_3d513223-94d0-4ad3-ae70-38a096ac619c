import { prisma } from './prisma.js';
import { ID_DATA } from '@/data/items';
import { CONFIG } from '@/data/templates';
import { DOPPLER_NAMES } from '@/data/constants';
import crypto from 'crypto';

// API配置
const API_CONFIG = {
  baseURL: 'https://gw-openapi.youpin898.com',
  appKey: '5255157',
  privateKey: `MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDiMSKvKHLg7SeQ6E+jRrywmHJ7pIU/tiiPx6quXKgyBbWdWFAfXmOaQALT5XduTuYqXm5jt1fQ7HfItjbPTctJ6G/J8EBizs4TVQT1dWFdYEXAHmm0aVaoVf+ZC13lnaj1ybwzQU8u0P5DTLPPFS9cKNDH1c8i2TOBWEnXhO2o9vDVyonOQ67j+dXCIZZiY0n/+mGcd2spwtRquRXvWAF0tAWg4mcwKwzUMEL9zUHIzSKD5r7TwKTYz6UjiMCWAvtat3GFI3CRXGh8eZnVLvl9gBPXGE8CGDTBImTzoJcP5YwhDjyFVxbqdcMgu4tNMe0lLJO2HGW4amDXfsoed5JjAgMBAAECggEABMpdnbARnsnnCJ6i8SZSYLsZ0ZyOZecJvOHRr5euyEButEJmkAODwISSisJVYTSykzskw1/isD4R22Jgjstq/sm5dGFuO6l9m9M1I620vjGHyKXcP0Hr3+zSWP9woahRJ8N6BOvhLXCnse0x8bTVJ/KFWXYhyO8otBiWl8Xs8qMeCvAt3ha2s6TyEhwjL+fTmIeyrACs2NfDUlf+q2WY6YljhlgBcTszhbndA9Nh4zzryl/hNVzK2wXZd5KhzsmrE86OndD+j11M/YLmxyoQbQI4+xOwmCaGVobEEO+EdmxPxx2GPN38dC2tj5H7gQKHxQkHs4e2HCWF11HONAZdIQKBgQD7CiBKaKQsA7vZ6alUPF7ZtL4f35M4mj/a0IpdPs4YIL74IfZktjUG9v1nd9BhWXoeGmwJYGd0heUeR/G5GEWZJfRYRd7Q6wzCyzjjShtZMDjUUcJZxLomgCFC7nBiV/1mQwsGwkkPT3UueBgJHBs8ye9UuWYHRFdGzN+xvCjOJQKBgQDmqVGRm18uVlj22wN0TsaPNrVlNIcVgh6cQcc/z3mftmRB7O8ACPdcApGFofL4TCHEW4XaG+yy8KpAgk28t2GO79mFoLkeehJZL22pntc0wtSHlLRzbL764OSewWRUFXVj8bER6KOMvUiPQ6ahXPqCsiWAizT1uAP65olraZSj5wKBgQCguFzwBoqE+2b7HpOgMH5xVzwZ3+O7a1XSW40aIamE3QImjmfXFqhgayTVZ2nryYNbXoMohaX2ffqwJlNls1prsFb9ZM5AZxvmYoFrUvirYyofFLEfE8ox9/pThaBB9h9vpyCaFSz9NlvJgm2w92OgyZAGaCQgJurRkzzr55EgWQKBgBL870UQSEaVPx+bKe6iKYBSnZ4mM9SWcJSmaZOcLcPzDM/MhGQ5WLuPTpF0on7ELadvbVZmJBca7rj+wQ1+/x34KqzwbSzQGcCTwfBLriWewEYk/LHfytz2NHvJKcwuNAq1M8FoqEYGsvlwNUBlWg32QlYRn5t2uYUnAzr/ZwgTAoGBALUmp+44JDZivO4zVevKG4Er+FWX1bH+Ir8fSasUYgv5R1Rpue5AchRpAxOEarS1f8/Mw+IuYKrtSNUVGd2PlfXjTwnkoQGDwsiEovvI6mxT8cAzhzsOuaHJAYjTH1tf9Q7LidEGWTWzG/hMiGgeWqqVy0EbuTxLQiR0THtPay2B`,
  timeout: 30000
};

// 时间戳格式化
function formatTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// RSA签名生成
function generateSign(params) {
  try {
    // 1. 移除sign参数（如果存在）
    const signParams = { ...params };
    delete signParams.sign;

    // 2. 按ASCII码排序参数
    const sortedKeys = Object.keys(signParams).sort();

    // 3. 拼接参数：key + JSON.stringify(value)
    let signString = '';
    for (const key of sortedKeys) {
      const value = signParams[key];
      if (value !== undefined && value !== null && value !== '') {
        signString += key + JSON.stringify(value);
      }
    }

    // 4. 将Base64私钥转换为PEM格式
    const privateKeyPem = `-----BEGIN PRIVATE KEY-----\n${API_CONFIG.privateKey.match(/.{1,64}/g)?.join('\n')}\n-----END PRIVATE KEY-----`;

    // 5. 使用SHA256withRSA算法签名
    const sign = crypto.createSign('RSA-SHA256');
    sign.update(signString);

    // 6. 使用私钥签名并返回Base64编码
    const signature = sign.sign(privateKeyPem, 'base64');

    return signature;
  } catch (error) {
    // 降级处理 - 使用简单的哈希签名
    try {
      const sortedKeys = Object.keys(params).sort();
      let signString = '';
      for (const key of sortedKeys) {
        if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
          signString += key + JSON.stringify(params[key]);
        }
      }

      const hash = crypto.createHash('sha256').update(signString).digest('base64');
      return hash;
    } catch (fallbackError) {
      return 'emergency_fallback_' + Date.now();
    }
  }
}

// 从悠悠API获取单个模板的数据
async function fetchTemplateData(templateId, dopplerProperty, wearRange, paintSeeds) {
  const allItems = [];
  let floorPrice = null;
  let floorPriceItem = null;

  try {
    // 查询1-5页数据
    for (let page = 1; page <= 5; page++) {
      const goodsRequestData = {
        timestamp: formatTimestamp(),
        appKey: API_CONFIG.appKey,
        templateId: templateId.toString(),
        dopplerProperty: dopplerProperty,
        pageSize: '200',
        page: page.toString(),
        sortType: '1'
      };

      // 如果有磨损度范围，添加磨损度查询参数
      if (wearRange && wearRange.length === 2) {
        goodsRequestData.abradeStartInterval = wearRange[0].toString();
        goodsRequestData.abradeEndInterval = wearRange[1].toString();
      }

      goodsRequestData.sign = generateSign(goodsRequestData);

      const goodsResponse = await fetch(
        `${API_CONFIG.baseURL}/open/v1/api/goodsQuery`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'YouPin-Frontend/1.0.0',
            'Accept': 'application/json'
          },
          body: JSON.stringify(goodsRequestData)
        }
      );

      const goodsData = await goodsResponse.json();

      if (goodsResponse.ok && goodsData.code === 0 && goodsData.data && goodsData.data.length > 0) {
        // 转换商品数据格式
        let convertedItems = goodsData.data.map((item) => ({
          templateId: item.templateId,
          templateHashName: '', // 将在调用处填充
          templateName: item.commodityName,
          iconUrl: '', // 将在调用处填充
          exteriorName: '崭新出厂',
          rarityName: '隐秘',
          minSellPrice: item.commodityPrice,
          referencePrice: item.commodityPrice,
          sellNum: 1,
          paintSeed: item.commodityPaintSeed || Math.floor(Math.random() * 1000),
          abrade: item.commodityAbrade || (Math.random() * 0.8).toFixed(4)
        }));

        // 记录底价（第一页第一个商品的价格）
        if (page === 1 && convertedItems.length > 0 && floorPriceItem === null) {
          floorPriceItem = convertedItems[0];
          floorPrice = parseFloat(convertedItems[0].minSellPrice);
        }

        // 如果配置了特定的图案种子，则进行筛选
        if (paintSeeds && paintSeeds.length > 0) {
          convertedItems = convertedItems.filter((item) =>
            paintSeeds.includes(item.paintSeed)
          );
        }

        allItems.push(...convertedItems);

        // 如果这一页没有数据，说明后面也没有了
        if (goodsData.data.length === 0) {
          break;
        }
      } else {
        // 如果某一页失败，继续查询下一页
        continue;
      }
    }
  } catch (error) {
    console.error('获取模板数据失败:', error);
  }

  // 按价格排序所有商品
  allItems.sort((a, b) => parseFloat(a.minSellPrice) - parseFloat(b.minSellPrice));

  return {
    items: allItems,
    floorPrice,
    floorPriceItem
  };
}

// 缓存指定等级的所有数据
export async function cacheDataForTier(tier = 'T1') {
  console.log(`开始缓存 ${tier} 等级数据...`);
  
  try {
    // 更新缓存状态为正在更新
    await prisma.cacheStatus.upsert({
      where: { tier },
      update: { 
        status: 'updating',
        errorMsg: null,
        updatedAt: new Date()
      },
      create: { 
        tier,
        status: 'updating',
        lastUpdated: new Date()
      }
    });

    // 清除旧的缓存数据
    await prisma.cachedTemplate.deleteMany({
      where: { tier }
    });

    const templatesConfig = tier === 'T1' ? CONFIG.templatesT1 :
      tier === 'T2' ? CONFIG.templatesT2 :
        CONFIG.templatesT1;

    let totalItems = 0;

    // 遍历所有模板配置
    for (const [templateName, variants] of Object.entries(templatesConfig)) {
      // 在id.js数据中查找匹配的模板
      const baseMatchedIdData = ID_DATA.find(item => {
        const baseName = item.name.replace(/\s*\([^)]*\)$/, '');
        return baseName === templateName;
      });

      if (!baseMatchedIdData) {
        continue;
      }

      // 处理每个变体
      for (const variant of variants) {
        const { paintSeeds, dopplerProperty, wearLevel, wearRange, wearName } = variant;

        // 为当前变体找到对应的ID
        let matchedIdData;
        if (wearName) {
          const fullNameWithWear = `${templateName} (${wearName})`;
          matchedIdData = ID_DATA.find(item => item.name === fullNameWithWear);
        } else {
          matchedIdData = baseMatchedIdData;
        }

        if (!matchedIdData) {
          continue;
        }

        // 如果paintSeeds为空，跳过该变体
        if (!paintSeeds || paintSeeds.length === 0) {
          continue;
        }

        // 获取显示名称
        const displayName = wearName ||
          (dopplerProperty !== undefined && dopplerProperty in DOPPLER_NAMES ?
            DOPPLER_NAMES[dopplerProperty] : '') ||
          `属性${dopplerProperty || wearLevel}`;

        // 从API获取数据
        const { items, floorPrice, floorPriceItem } = await fetchTemplateData(
          matchedIdData.id,
          dopplerProperty,
          wearRange,
          paintSeeds
        );

        // 创建缓存模板记录
        const cachedTemplate = await prisma.cachedTemplate.create({
          data: {
            templateName,
            dopplerName: displayName,
            dopplerProperty,
            wearLevel,
            wearName,
            wearRange: wearRange ? JSON.stringify(wearRange) : null,
            paintSeedsCount: paintSeeds.length,
            floorPrice,
            floorPriceItem: floorPriceItem ? JSON.stringify(floorPriceItem) : null,
            tier
          }
        });

        // 批量创建缓存商品记录
        if (items.length > 0) {
          const cachedItems = items.map(item => ({
            templateId: item.templateId,
            templateHashName: matchedIdData.hashName,
            templateName: item.templateName,
            iconUrl: `https://youpin.img898.com/csgo/template/${matchedIdData.id}.png`,
            exteriorName: item.exteriorName,
            rarityName: item.rarityName,
            minSellPrice: parseFloat(item.minSellPrice),
            referencePrice: parseFloat(item.referencePrice),
            sellNum: item.sellNum,
            paintSeed: item.paintSeed,
            abrade: item.abrade.toString(),
            cachedTemplateId: cachedTemplate.id
          }));

          await prisma.cachedItem.createMany({
            data: cachedItems
          });

          totalItems += items.length;
        }

        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // 更新缓存状态为完成
    await prisma.cacheStatus.upsert({
      where: { tier },
      update: { 
        status: 'idle',
        lastUpdated: new Date(),
        totalItems,
        errorMsg: null,
        updatedAt: new Date()
      },
      create: { 
        tier,
        status: 'idle',
        lastUpdated: new Date(),
        totalItems
      }
    });

    console.log(`${tier} 等级数据缓存完成，共缓存 ${totalItems} 个商品`);
    return { success: true, totalItems };

  } catch (error) {
    console.error(`缓存 ${tier} 等级数据失败:`, error);
    
    // 更新缓存状态为错误
    await prisma.cacheStatus.upsert({
      where: { tier },
      update: { 
        status: 'error',
        errorMsg: error.message,
        updatedAt: new Date()
      },
      create: { 
        tier,
        status: 'error',
        lastUpdated: new Date(),
        errorMsg: error.message
      }
    });

    return { success: false, error: error.message };
  }
}

// 从缓存获取数据
export async function getCachedData(tier = 'T1') {
  try {
    const cachedTemplates = await prisma.cachedTemplate.findMany({
      where: { tier },
      include: {
        items: true
      },
      orderBy: [
        { templateName: 'asc' },
        { dopplerProperty: 'asc' },
        { wearLevel: 'asc' }
      ]
    });

    // 转换为与原API相同的格式
    const results = cachedTemplates.map(template => {
      const result = {
        templateName: template.templateName,
        dopplerName: template.dopplerName,
        wearRange: template.wearRange ? JSON.parse(template.wearRange) : null,
        paintSeedsCount: template.paintSeedsCount,
        floorPrice: template.floorPrice,
        floorPriceItem: template.floorPriceItem ? JSON.parse(template.floorPriceItem) : null,
        items: template.items.map(item => ({
          templateId: item.templateId,
          templateHashName: item.templateHashName,
          templateName: item.templateName,
          iconUrl: item.iconUrl,
          exteriorName: item.exteriorName,
          rarityName: item.rarityName,
          minSellPrice: item.minSellPrice.toString(),
          referencePrice: item.referencePrice.toString(),
          sellNum: item.sellNum,
          paintSeed: item.paintSeed,
          abrade: item.abrade
        }))
      };

      // 添加dopplerProperty或wearLevel
      if (template.dopplerProperty !== null) {
        result.dopplerProperty = template.dopplerProperty;
      }
      if (template.wearLevel !== null) {
        result.wearLevel = template.wearLevel;
        result.wearName = template.wearName;
      }

      return result;
    });

    return results;
  } catch (error) {
    console.error('获取缓存数据失败:', error);
    return [];
  }
}

// 获取缓存状态
export async function getCacheStatus(tier = 'T1') {
  try {
    const status = await prisma.cacheStatus.findUnique({
      where: { tier }
    });
    return status;
  } catch (error) {
    console.error('获取缓存状态失败:', error);
    return null;
  }
}
