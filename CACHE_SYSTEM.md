# 缓存系统说明

## 概述

本系统实现了一个智能缓存机制，在半点和整点自动调用悠悠接口获取饰品数据并缓存到数据库中。前端页面优先使用缓存数据，大大提高了响应速度并减少了对外部API的依赖。

## 主要特性

### 1. 定时缓存更新
- **执行时间**: 每小时的0分和30分（例如：9:00, 9:30, 10:00, 10:30...）
- **缓存内容**: T1和T2等级的所有饰品数据
- **自动通知**: 缓存完成后会发送企业微信通知

### 2. 智能数据获取
- **优先缓存**: API优先返回缓存数据
- **自动回退**: 缓存不可用时自动使用实时API
- **缓存有效期**: 2小时，超时自动回退到实时数据

### 3. 缓存管理
- **状态监控**: 实时查看缓存状态和更新时间
- **手动刷新**: 支持手动触发缓存更新
- **错误处理**: 自动记录和显示错误信息

## 数据库结构

### CachedTemplate (缓存模板表)
- 存储模板基本信息和配置
- 包含底价信息和图案种子数量
- 按等级(tier)分类存储

### CachedItem (缓存商品表)
- 存储具体的商品数据
- 包含价格、磨损度、图案种子等信息
- 与模板表关联

### CacheStatus (缓存状态表)
- 记录每个等级的缓存状态
- 包含最后更新时间和错误信息
- 用于监控缓存健康状态

## API接口

### 1. 模板数据接口
```
GET /api/templates?tier=T1&useCache=true
```
- `tier`: 等级 (T1, T2)
- `useCache`: 是否使用缓存 (默认true)

### 2. 溢价商品接口
```
GET /api/premium-items?tier=T1&useCache=true
```
- 自动使用缓存数据进行溢价筛选
- 提高溢价查询响应速度

### 3. 缓存管理接口
```
GET /api/cache                    # 获取缓存状态
GET /api/cache?tier=T1           # 获取特定等级状态
POST /api/cache                   # 手动刷新缓存
```

## 使用方法

### 1. 启动应用
```bash
# 开发环境
npm run dev

# 生产环境
npm run build
npm run start

# 或使用缓存启动脚本
npm run start:cache
```

### 2. 管理缓存
访问管理页面: `http://localhost:3001/admin/cache`

功能包括：
- 查看T1和T2等级的缓存状态
- 手动刷新单个等级或全部缓存
- 查看错误信息和更新时间

### 3. API调用
```javascript
// 获取缓存数据（推荐）
fetch('/api/templates?tier=T1')

// 强制使用实时数据
fetch('/api/templates?tier=T1&useCache=false')

// 查看缓存状态
fetch('/api/cache')

// 手动刷新缓存
fetch('/api/cache', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ action: 'refresh', tier: 'T1' })
})
```

## 配置说明

### 1. 定时任务配置
在 `src/lib/scheduler.js` 中可以修改：
- 执行时间（当前为每30分钟）
- 企业微信通知配置
- 缓存策略

### 2. 缓存有效期
在 `src/app/api/templates/route.js` 中可以修改：
- 缓存有效期（当前为2小时）
- 缓存检查逻辑

### 3. 数据库配置
缓存数据存储在SQLite数据库中，表结构在 `prisma/schema.prisma` 中定义。

## 监控和维护

### 1. 日志监控
- 定时任务执行日志
- 缓存更新成功/失败日志
- API调用数据源日志

### 2. 企业微信通知
- 缓存更新完成通知
- 缓存更新失败通知
- 包含详细的执行时间和结果统计

### 3. 状态检查
- 定期检查缓存状态页面
- 监控缓存更新时间
- 关注错误信息和处理

## 故障排除

### 1. 缓存更新失败
- 检查网络连接
- 查看API密钥配置
- 检查数据库连接

### 2. 定时任务不执行
- 确认应用正常启动
- 检查时区设置
- 查看进程日志

### 3. 数据不一致
- 手动刷新缓存
- 检查API返回数据
- 重启应用服务

## 性能优化

### 1. 响应速度提升
- 缓存命中时响应时间 < 100ms
- 避免频繁的外部API调用
- 减少网络延迟影响

### 2. 资源使用优化
- 定时更新避免高峰期压力
- 批量数据库操作
- 合理的缓存有效期设置

### 3. 扩展性考虑
- 支持更多等级的缓存
- 可配置的更新频率
- 分布式缓存支持（未来）
