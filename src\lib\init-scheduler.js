import { startScheduler } from './scheduler.js';

let schedulerTask = null;

// 初始化调度器
export function initScheduler() {
  if (schedulerTask) {
    console.log('调度器已经在运行中');
    return schedulerTask;
  }

  try {
    schedulerTask = startScheduler();
    console.log('调度器初始化成功');
    return schedulerTask;
  } catch (error) {
    console.error('调度器初始化失败:', error);
    return null;
  }
}

// 获取调度器实例
export function getSchedulerTask() {
  return schedulerTask;
}

// 在服务器启动时自动初始化
if (typeof window === 'undefined') {
  // 只在服务器端运行
  console.log('服务器启动，初始化定时任务调度器...');
  initScheduler();
}
