import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";

// 在服务器端初始化定时任务
if (typeof window === 'undefined') {
  // 动态导入以避免客户端执行
  import('@/lib/init-scheduler').catch(console.error);
}

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "饰品监控助手饰品价格监控",
  description: "CS:GO饰品模板价格监控工具，实时查询多普勒系列饰品价格",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
