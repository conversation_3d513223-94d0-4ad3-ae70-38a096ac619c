import cron from 'node-cron';
import { cacheDataForTier } from './cache-service.js';

// 企业微信机器人配置
const WECHAT_WEBHOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a87de667-269b-4939-b9f2-81c9f88bce9b';

// 发送企业微信通知
async function sendWeChatNotification(message) {
  try {
    const response = await fetch(WECHAT_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        msgtype: 'markdown',
        markdown: {
          content: message
        }
      })
    });

    if (!response.ok) {
      console.error('企业微信通知发送失败:', response.status, response.statusText);
    } else {
      console.log('企业微信通知发送成功');
    }
  } catch (error) {
    console.error('发送企业微信通知时出错:', error);
  }
}

// 缓存数据任务
async function cacheDataTask() {
  const startTime = Date.now();
  console.log('开始定时缓存任务...');

  try {
    // 缓存T1等级数据
    const t1Result = await cacheDataForTier('T1');
    
    // 缓存T2等级数据
    const t2Result = await cacheDataForTier('T2');

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    const message = `## 🔄 定时缓存任务完成

**执行时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
**耗时**: ${duration}秒

### 📊 缓存结果
- **T1等级**: ${t1Result.success ? `✅ 成功，共${t1Result.totalItems}个商品` : `❌ 失败：${t1Result.error}`}
- **T2等级**: ${t2Result.success ? `✅ 成功，共${t2Result.totalItems}个商品` : `❌ 失败：${t2Result.error}`}

### 🔗 访问链接
[点击查看最新数据](http://localhost:3001)

---
*数据已自动更新，前端页面将显示最新缓存数据*`;

    await sendWeChatNotification(message);
    console.log('定时缓存任务完成');

  } catch (error) {
    console.error('定时缓存任务失败:', error);
    
    const errorMessage = `## ❌ 定时缓存任务失败

**执行时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
**错误信息**: ${error.message}

请检查系统状态并手动重试。

---
*系统将在下次定时任务时自动重试*`;

    await sendWeChatNotification(errorMessage);
  }
}

// 启动定时任务
export function startScheduler() {
  console.log('启动定时任务调度器...');

  // 每30分钟执行一次（半点和整点）
  // 0 0,30 * * * 表示每小时的0分和30分执行
  const task = cron.schedule('0 0,30 * * *', async () => {
    console.log('触发定时缓存任务');
    await cacheDataTask();
  }, {
    scheduled: false
  });

  // 启动任务
  task.start();
  console.log('定时任务已启动，将在每小时的0分和30分执行缓存更新');

  // 可选：立即执行一次缓存任务（用于测试）
  if (process.env.NODE_ENV === 'development') {
    console.log('开发环境：立即执行一次缓存任务');
    setTimeout(() => {
      cacheDataTask();
    }, 5000); // 5秒后执行
  }

  return task;
}

// 停止定时任务
export function stopScheduler(task) {
  if (task) {
    task.stop();
    console.log('定时任务已停止');
  }
}

// 手动触发缓存任务
export async function triggerCacheTask() {
  console.log('手动触发缓存任务');
  return await cacheDataTask();
}
