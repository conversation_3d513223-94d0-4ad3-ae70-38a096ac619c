import { NextResponse } from 'next/server';
import { cacheDataForTier, getCacheStatus } from '@/lib/cache-service';
import { triggerCacheTask } from '@/lib/scheduler';

// GET - 获取缓存状态
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const tier = searchParams.get('tier');

    if (tier) {
      // 获取特定等级的缓存状态
      const status = await getCacheStatus(tier);
      return NextResponse.json({
        code: 0,
        msg: '获取缓存状态成功',
        data: status
      });
    } else {
      // 获取所有等级的缓存状态
      const t1Status = await getCacheStatus('T1');
      const t2Status = await getCacheStatus('T2');
      
      return NextResponse.json({
        code: 0,
        msg: '获取缓存状态成功',
        data: {
          T1: t1Status,
          T2: t2Status
        }
      });
    }
  } catch (error) {
    console.error('获取缓存状态失败:', error);
    return NextResponse.json({
      code: -1,
      msg: '获取缓存状态失败: ' + error.message,
      data: null
    }, { status: 500 });
  }
}

// POST - 手动刷新缓存
export async function POST(request) {
  try {
    const { tier, action } = await request.json();

    if (action === 'refresh') {
      if (tier && (tier === 'T1' || tier === 'T2')) {
        // 刷新特定等级的缓存
        const result = await cacheDataForTier(tier);
        
        return NextResponse.json({
          code: result.success ? 0 : -1,
          msg: result.success ? `${tier}等级缓存刷新成功` : `${tier}等级缓存刷新失败`,
          data: result
        });
      } else if (tier === 'all') {
        // 刷新所有等级的缓存
        const result = await triggerCacheTask();
        
        return NextResponse.json({
          code: 0,
          msg: '全部缓存刷新任务已触发',
          data: result
        });
      } else {
        return NextResponse.json({
          code: -1,
          msg: '无效的等级参数，请使用 T1、T2 或 all',
          data: null
        }, { status: 400 });
      }
    } else {
      return NextResponse.json({
        code: -1,
        msg: '无效的操作，请使用 refresh',
        data: null
      }, { status: 400 });
    }
  } catch (error) {
    console.error('缓存操作失败:', error);
    return NextResponse.json({
      code: -1,
      msg: '缓存操作失败: ' + error.message,
      data: null
    }, { status: 500 });
  }
}
