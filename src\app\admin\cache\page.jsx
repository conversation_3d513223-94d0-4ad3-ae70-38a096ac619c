'use client';

import { useState, useEffect } from 'react';

export default function CacheManagePage() {
  const [cacheStatus, setCacheStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // 获取缓存状态
  const fetchCacheStatus = async () => {
    try {
      const response = await fetch('/api/cache');
      const data = await response.json();
      if (data.code === 0) {
        setCacheStatus(data.data);
      }
    } catch (error) {
      console.error('获取缓存状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新缓存
  const refreshCache = async (tier) => {
    setRefreshing(true);
    try {
      const response = await fetch('/api/cache', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'refresh',
          tier: tier
        })
      });
      
      const data = await response.json();
      alert(data.msg);
      
      // 刷新状态
      await fetchCacheStatus();
    } catch (error) {
      console.error('刷新缓存失败:', error);
      alert('刷新缓存失败');
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchCacheStatus();
    
    // 每30秒自动刷新状态
    const interval = setInterval(fetchCacheStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const formatDate = (dateString) => {
    if (!dateString) return '未知';
    return new Date(dateString).toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'idle': return 'text-green-600';
      case 'updating': return 'text-blue-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'idle': return '空闲';
      case 'updating': return '更新中';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载缓存状态中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">缓存管理</h1>
            <button
              onClick={() => refreshCache('all')}
              disabled={refreshing}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {refreshing ? '刷新中...' : '刷新全部缓存'}
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* T1等级缓存状态 */}
            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-800">T1等级缓存</h2>
                <button
                  onClick={() => refreshCache('T1')}
                  disabled={refreshing}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 disabled:opacity-50"
                >
                  刷新T1
                </button>
              </div>
              
              {cacheStatus?.T1 ? (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">状态:</span>
                    <span className={getStatusColor(cacheStatus.T1.status)}>
                      {getStatusText(cacheStatus.T1.status)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">最后更新:</span>
                    <span className="text-gray-800">
                      {formatDate(cacheStatus.T1.lastUpdated)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">商品总数:</span>
                    <span className="text-gray-800">{cacheStatus.T1.totalItems}</span>
                  </div>
                  {cacheStatus.T1.errorMsg && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                      <span className="text-red-600 text-sm">{cacheStatus.T1.errorMsg}</span>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-500">暂无缓存数据</p>
              )}
            </div>

            {/* T2等级缓存状态 */}
            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-800">T2等级缓存</h2>
                <button
                  onClick={() => refreshCache('T2')}
                  disabled={refreshing}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 disabled:opacity-50"
                >
                  刷新T2
                </button>
              </div>
              
              {cacheStatus?.T2 ? (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">状态:</span>
                    <span className={getStatusColor(cacheStatus.T2.status)}>
                      {getStatusText(cacheStatus.T2.status)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">最后更新:</span>
                    <span className="text-gray-800">
                      {formatDate(cacheStatus.T2.lastUpdated)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">商品总数:</span>
                    <span className="text-gray-800">{cacheStatus.T2.totalItems}</span>
                  </div>
                  {cacheStatus.T2.errorMsg && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                      <span className="text-red-600 text-sm">{cacheStatus.T2.errorMsg}</span>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-500">暂无缓存数据</p>
              )}
            </div>
          </div>

          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">定时任务说明</h3>
            <ul className="text-blue-700 space-y-1">
              <li>• 系统会在每小时的0分和30分自动更新缓存数据</li>
              <li>• 前端页面优先使用缓存数据，提高响应速度</li>
              <li>• 缓存数据超过2小时会自动回退到实时API调用</li>
              <li>• 可以手动刷新缓存以获取最新数据</li>
            </ul>
          </div>

          <div className="mt-4 text-center">
            <button
              onClick={fetchCacheStatus}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              刷新状态
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
